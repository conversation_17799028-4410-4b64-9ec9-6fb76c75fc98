"use client";
import React, { useState } from "react";
import Image from "next/image";

interface Testimonial {
  id: number;
  quote: string;
  name: string;
  location: string;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    quote:
      "I have gained more turn over due to Koolboks. I have eliminated the cost of fuel. I have ventured into ice block business and even nicknamed the freezer '<PERSON><PERSON>g<PERSON>' meaning 'power authority is put to shame'",
    name: "Mrs <PERSON>",
    location: "Abuja, Nigeria",
    image: "/images/testimonial-person.png",
  },
  {
    id: 2,
    quote:
      "Koolboks has revolutionized my business operations. The reliability and efficiency have exceeded my expectations, allowing me to expand my services.",
    name: "Mr <PERSON>",
    location: "Lagos, Nigeria",
    image: "/images/testimonial-2.jpg",
  },
  {
    id: 3,
    quote:
      "The solar-powered refrigeration solution has been a game changer for our community. No more worries about power outages affecting our business.",
    name: "Mrs <PERSON>",
    location: "Kano, Nigeria",
    image: "/images/testimonial-3.jpg",
  },
];

const TestimonialSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  const goToTestimonial = (index: number) => {
    setCurrentTestimonial(index);
  };

  const current = testimonials[currentTestimonial];

  return (
    <div className="">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-[40px] lg:gap-[60px]">
          {/* Quote Section */}
          <div className="w-full lg:w-1/2 lg:order-1">
            {/* Quote Icon */}
            <div className="w-[60px] h-[60px] bg-primary-500 rounded-full flex items-center justify-center mb-[30px]">
              <Image
                src={"/icons/quote.svg"}
                alt="quote"
                width={29.709976196289062}
                height={24}
              />
            </div>
            {/* Quote Text */}
            <blockquote className="text-medium text-woodsmoke-950 mb-[16px] font-medium">
              &ldquo;{current.quote}&rdquo;
            </blockquote>

            {/* Customer Info */}
            <div className="mb-[8px]">
              <p className="font-bold text-regular text-woodsmoke-950 mb-[4px]">
                {current.name}
              </p>
              <p className="text-small text-woodsmoke-950">
                {current.location}
              </p>
            </div>
          </div>

          {/* Image Section */}
          <div className="w-full lg:w-1/2 order-1 lg:order-2">
            <div className="relative">
              <div className="aspect-[4/3] relative overflow-hidden rounded-[20px]">
                <Image
                  src={current.image}
                  alt={`${current.name} testimonial`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 50vw"
                />
              </div>
              {/* Navigation */}
              <div className="flex items-center justify-between mt-[30px]">
                <div className="flex gap-[20px]">
                  {/* Previous Button */}
                  <button
                    onClick={prevTestimonial}
                    className="w-[40px] h-[40px] bg-primary-100 hover:bg-primary-200 rounded-full flex items-center justify-center transition-colors duration-200"
                    aria-label="Previous testimonial"
                  >
                    <Image
                      src={"/icons/ArrowLeft.svg"}
                      alt="ArrowLeft"
                      width={20}
                      height={20}
                    />
                  </button>

                  {/* Next Button */}
                  <button
                    onClick={nextTestimonial}
                    className="w-[40px] h-[40px] bg-primary-100 hover:bg-primary-200 rounded-full flex items-center justify-center transition-colors duration-200"
                    aria-label="Next testimonial"
                  >
                    <Image
                      src={"/icons/ArrowRight.svg"}
                      alt="ArrowRight"
                      width={20}
                      height={20}
                    />
                  </button>
                </div>

                {/* Pagination Dots */}
                <div className="flex items-center gap-[8px] ml-[20px]">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToTestimonial(index)}
                      className={`w-[10px] h-[10px] rounded-full transition-colors duration-200 ${
                        index === currentTestimonial
                          ? "bg-primary-500"
                          : "bg-primary-100 hover:bg-primary-200"
                      }`}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;
