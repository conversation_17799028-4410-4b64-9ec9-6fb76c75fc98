"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import Image from "next/image";
import gsap from "gsap";
import { cn } from "@/lib/utils";
import TrackingCard from "./TrackingCard";

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const slidesRef = useRef<HTMLDivElement[]>([]);
  const sliderRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  const slides = [
    {
      id: 0,
      image: "/images/hero-img-1.png",
    },
    {
      id: 1,
      image: "/images/hero-img-2.png",
    },
    {
      id: 2,
      image: "/images/hero-img-3.png",
    },
  ];

  // Define goToSlide with useCallback to avoid dependency issues
  const goToSlide = useCallback(
    (index: number) => {
      if (currentSlide === index) return;

      if (timelineRef.current) {
        timelineRef.current.clear();
      }

      const tl = gsap.timeline();

      // Fade out current slide
      tl.to(slidesRef.current[currentSlide], {
        autoAlpha: 0,
        duration: 0.8,
        ease: "power2.out",
      });

      // Fade in new slide
      tl.to(
        slidesRef.current[index],
        {
          autoAlpha: 1,
          duration: 0.8,
          ease: "power2.in",
        },
        "-=0.5"
      );

      setCurrentSlide(index);
    },
    [currentSlide]
  );

  // Initialize the slider and set up auto-rotation
  useEffect(() => {
    // Hide all slides except the first one
    slidesRef.current.forEach((slide, index) => {
      if (index !== 0) {
        gsap.set(slide, { autoAlpha: 0 });
      }
    });

    // Create the timeline instance
    timelineRef.current = gsap.timeline();

    let interval: NodeJS.Timeout | null = null;

    // Only set up automatic slide change if not paused
    if (!isPaused) {
      interval = setInterval(() => {
        const nextSlide = (currentSlide + 1) % slides.length;
        goToSlide(nextSlide);
      }, 10000); // Change slide every 10 seconds
    }

    return () => {
      // Cleanup
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [currentSlide, slides.length, goToSlide, isPaused]);

  return (
    <div className="relative h-screen w-full overflow-hidden">
      {/* Carousel container */}
      <div ref={sliderRef} className="h-full w-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            ref={(el) => {
              if (el) slidesRef.current[index] = el;
            }}
            className="absolute inset-0 h-full w-full"
          >
            <Image
              src={slide.image}
              alt={`Hero slide ${index + 1}`}
              fill
              priority={index === 0}
              className="object-cover"
            />

            {/* You can add text or other content overlaying each image here */}
            <div className="absolute inset-0 bg-black/30">
              <div className="w-full container mx-auto px-2 md:px-3 lg:px-4 h-full flex items-center mt-[60px]">
                <div className="text-white max-w-xl">
                  <h1 className="h1-semibold">
                    Never Lose Sight of Your{" "}
                    <span className="text-primary-500">Shipments.</span>
                  </h1>
                  <p className="text-medium font-semibold text-woodsmoke-100 my-[20px]">
                    KOOL LOGISTICS makes it easy to create and track shipments.
                    You can choose from a variety of drivers and companies to
                    deliver your shipments safely and quickly.
                  </p>
                  <TrackingCard
                    onUserInteraction={(isInteracting) =>
                      setIsPaused(isInteracting)
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Vertical indicators on the right */}
      <div className="absolute hidden sm:flex right-8 top-1/2 transform -translate-y-1/2 flex-col gap-3 p-[8px] bg-white/20 rounded-[22px]">
        {slides.map((slide, index) => (
          <button
            key={slide.id}
            onClick={() => goToSlide(index)}
            className={cn(
              "w-[16px] h-[16px] rounded-full transition-all duration-300 flex items-center justify-center",
              currentSlide === index
                ? "bg-primary-500"
                : "bg-primary-100 hover:bg-white/70"
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default HeroSection;
