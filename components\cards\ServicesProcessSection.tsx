import Image from "next/image";
import React from "react";

const ServicesProcessSection = () => {
  return (
    <div className="my-[60px] md:my-[90px] lg:my-[128px] flex flex-col lg:flex-row gap-[30px] md:gap-[40px] lg:gap-[48px]">
      <div className="w-full lg:w-1/2 relative h-[300px] sm:h-[350px] md:h-[400px]">
        <Image
          src="/images/supply-chain.png"
          alt="supply chain"
          width={576}
          height={472}
          className="rounded-[20px] object-cover h-full w-full"
        />
      </div>
      <div className="w-full lg:w-1/2">
        <div className="flex">
          <Image
            src={"/icons/package-primary.svg"}
            alt="package"
            width={13}
            height={13.996332168579102}
          />
          <p className="text-small text-woodsmoke-800 font-semibold ml-[11.5px]">
            OUR KOOL PROCESS
          </p>
        </div>
        <h3 className="h3-semibold mt-[16px] sm:mt-[20px] mb-[24px] sm:mb-[40px]">
          How we deliver Kool service
        </h3>
        <div className="border-1 p-[12px] sm:p-[16px] rounded-[8px] mb-[16px]">
          <div className="flex flex-col sm:flex-row sm:justify-between">
            <div className="mb-2 sm:mb-0">
              <span className="text-primary-500 font-bold text-[20px] sm:text-[24px] leading-[150%] tracking-[0px] mr-[12px]">
                01
              </span>
              <span className="text-woodsmoke-950 font-medium text-[18px] sm:text-[24px] leading-[150%] tracking-[0px]">
                Order Processing
              </span>
            </div>

            <Image
              src={"/icons/ArrowUpRight.svg"}
              alt="ArrowUpRight.svg"
              width={14.252889633178711}
              height={14.252889633178711}
              className="hidden sm:block"
            />
          </div>
          <p className="text-regular text-woodsmoke-800 mt-[12px] sm:mt-[16px]">
            Koolboks offers a range of solar-powered refrigerators designed to
            meet diverse needs. Whether for homes, businesses, our products
            ensure reliable cooling without reliance on traditional energy
            sources.
          </p>
        </div>
        <div className="border-1 p-[12px] sm:p-[16px] rounded-[8px] mb-[16px]">
          <div className="flex flex-col sm:flex-row sm:justify-between">
            <div className="mb-2 sm:mb-0">
              <span className="text-primary-500 font-bold text-[20px] sm:text-[24px] leading-[150%] tracking-[0px] mr-[12px]">
                02
              </span>
              <span className="text-woodsmoke-950 font-medium text-[18px] sm:text-[24px] leading-[150%] tracking-[0px]">
                Order Tracking
              </span>
            </div>

            <Image
              src={"/icons/ArrowDownRight.svg"}
              alt="ArrowDownRight.svg"
              width={14.252889633178711}
              height={14.252889633178711}
              className="hidden sm:block"
            />
          </div>
        </div>
        <div className="border-1 p-[12px] sm:p-[16px] rounded-[8px] mb-[16px]">
          <div className="flex flex-col sm:flex-row sm:justify-between">
            <div className="mb-2 sm:mb-0">
              <span className="text-primary-500 font-bold text-[20px] sm:text-[24px] leading-[150%] tracking-[0px] mr-[12px]">
                03
              </span>
              <span className="text-woodsmoke-950 font-medium text-[18px] sm:text-[24px] leading-[150%] tracking-[0px]">
                Order Delivery
              </span>
            </div>

            <Image
              src={"/icons/ArrowDownRight.svg"}
              alt="ArrowUpRight.svg"
              width={14.252889633178711}
              height={14.252889633178711}
              className="hidden sm:block"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServicesProcessSection;
