'use client'
import React, { useState } from 'react'
import Image from 'next/image'


const QouteForm = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    services: [] as string[],
    shipmentOrigin: '',
    shipmentDestination: '',
    measurement: '',
    totalWeight: '',
    notes: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleServiceChange = (service: string) => {
    setFormData(prev => ({
      ...prev,
      services: [service] // Only allow one service selection
    }))
  }

  return (
    <div className="min-h-screen bg-white pt-14">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        input[type="checkbox"]:checked::after {
          content: "✓";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 10px;
          font-weight: bold;
        }
      `}</style>
      {/* Header Section */}
      <section className="py-6 md:py-8 lg:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-4 md:mb-6">
            <Image
              src="/icons/cubeicon.svg"
              alt="Cube Icon"
              width={20}
              height={20}
              className="w-4 h-4 md:w-5 md:h-5"
            />
            <p className="text-[#454545] font-medium text-xs md:text-sm tracking-wider uppercase">REQUEST QUOTE</p>
          </div>
          <h1 className="font-manrope font-semibold text-[24px] md:text-[32px] lg:text-[40px] leading-[120%] tracking-[0%] text-center text-[#2D2D2D] max-w-4xl mx-auto">
            Get Your Quote Today – Fast, Easy,<br className="hidden md:block" />
            <span className="md:hidden"> </span>and Tailored to Your Needs!
          </h1>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-8 md:pb-12 lg:pb-20">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-8 lg:gap-16 items-start">
            {/* Image - Shows first on mobile, second on desktop */}
            <div className="relative order-1 lg:order-1">
              <div className="w-full h-[250px] sm:h-[300px] md:h-[400px] lg:h-[600px] rounded-[12px] md:rounded-[16px] lg:rounded-[24px] overflow-hidden shadow-sm">
                <Image
                  src="/images/call.svg"
                  alt="Customer service call"
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Form - Shows second on mobile, first on desktop */}
            <div className="order-2 lg:order-2 lg:h-[600px] lg:overflow-y-auto lg:scrollbar-hide" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
              {/* Personal Details */}
              <div className="space-y-4 md:space-y-6">
                <h2 className="text-[18px] md:text-[20px] font-semibold text-[#2D2D2D] mb-4 md:mb-6">Personal Details</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">First name</label>
                    <input
                      type="text"
                      name="firstName"
                      placeholder="First name"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Last name</label>
                    <input
                      type="text"
                      name="lastName"
                      placeholder="Last name"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Email</label>
                    <input
                      type="email"
                      name="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Phone number</label>
                    <div className="flex">
                      <select className="px-2 md:px-3 py-2 md:py-3 border border-[#E5E5E5] border-r-0 rounded-l-[6px] md:rounded-l-[8px] text-[13px] md:text-[14px] bg-white focus:outline-none focus:border-[#FF6B35]">
                        <option>NG</option>
                      </select>
                      <input
                        type="tel"
                        name="phoneNumber"
                        placeholder="+234 ************"
                        value={formData.phoneNumber}
                        onChange={handleInputChange}
                        className="flex-1 px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-r-[6px] md:rounded-r-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Services */}
              <div className="space-y-3 md:space-y-4 mt-6 md:mt-8">
                <h2 className="text-[18px] md:text-[20px] font-semibold text-[#2D2D2D] mb-3 md:mb-4">Services</h2>
                <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3 md:gap-4">
                  {['Fragile', 'Express Delivery', 'Insurance', 'Packaging'].map((service) => (
                    <label key={service} className="flex items-center gap-2 md:gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.services.includes(service)}
                        onChange={() => handleServiceChange(service)}
                        className="w-4 h-4 md:w-5 md:h-5 appearance-none border-2 border-[#E5E5E5] rounded-sm bg-white checked:bg-[#FF6B35] checked:border-[#FF6B35] focus:ring-2 focus:ring-[#FF6B35] focus:ring-opacity-50 relative"
                      />
                      <span className="text-[12px] md:text-[13px] text-[#666666]">{service}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Shipment Details */}
              <div className="space-y-3 md:space-y-4 mt-6 md:mt-8">
                <h2 className="text-[18px] md:text-[20px] font-semibold text-[#2D2D2D] mb-3 md:mb-4">Shipment Details</h2>

                <div className="space-y-3 md:space-y-4">
                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Shipment Origin</label>
                    <input
                      type="text"
                      name="shipmentOrigin"
                      placeholder="Address"
                      value={formData.shipmentOrigin}
                      onChange={handleInputChange}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>

                  <div className="space-y-1 md:space-y-2">
                    <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Shipment Destination</label>
                    <input
                      type="text"
                      name="shipmentDestination"
                      placeholder="Address"
                      value={formData.shipmentDestination}
                      onChange={handleInputChange}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                    <div className="space-y-1 md:space-y-2">
                      <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Select measurement</label>
                      <select
                        name="measurement"
                        value={formData.measurement}
                        onChange={handleInputChange}
                        className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] bg-white focus:outline-none focus:border-[#FF6B35]"
                      >
                        <option value="">Select options</option>
                        <option value="kg">Kilograms (kg)</option>
                        <option value="lbs">Pounds (lbs)</option>
                      </select>
                    </div>
                    <div className="space-y-1 md:space-y-2">
                      <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Total Weight</label>
                      <input
                        type="text"
                        name="totalWeight"
                        placeholder="Enter size"
                        value={formData.totalWeight}
                        onChange={handleInputChange}
                        className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-1 md:space-y-2 mt-6 md:mt-8">
                <label className="text-[12px] md:text-[13px] font-medium text-[#666666]">Notes</label>
                <textarea
                  name="notes"
                  rows={3}
                  value={formData.notes}
                  onChange={handleInputChange}
                  className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[13px] md:text-[14px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] resize-none"
                />
              </div>

              {/* Submit Button */}
              <div className="pt-4 md:pt-6">
                <button className="w-full bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-6 md:px-8 py-3 md:py-4 rounded-[6px] md:rounded-[8px] font-semibold text-[14px] md:text-[16px] transition-colors">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>


    </div>
  )
}

export default QouteForm
