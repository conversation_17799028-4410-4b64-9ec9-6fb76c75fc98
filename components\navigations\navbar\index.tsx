"use client";

import ROUTES from "@/constants/routes";
import { navLinks } from "@/constants";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import NavLinks from "./NavLinks";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  function toggleMenu(event: React.MouseEvent<HTMLElement>): void {
    setIsMenuOpen(!isMenuOpen);
  }

return (
      <nav className="fixed z-50 w-full bg-white/80 backdrop-blur-sm rounded-b-[12px]">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <Link href={ROUTES.HOME} className="">
          <Image
            src="/images/site-logo.png"
            width={118}
            height={56}
            alt="Kool logistics logo"
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:block">
          <NavLinks />
        </div>

        <div className="hidden md:block">
          <Link href={ROUTES.SIGN_IN}>
            <button className="btn-secondary-no-bg mr-2">Login</button>
          </Link>
          <Link href={ROUTES.SIGN_UP}>
            <button className="btn-primary">Sign Up</button>
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden bg-primary-500 p-[12px] rounded-[12px]"
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <Image src={"/icons/menu.svg"} alt="menu" width={18} height={13.5} />
        </button>
      </div>

      {/* Mobile Navigation Overlay */}
      {isMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-white/0 p-2 shadow-lg rounded-b-xl z-50 max-h-screen overflow-y-auto">
          <div className="p-5 bg-white rounded-[16px]">
            <div className="flex justify-between items-center mb-6">
              <Image
                src="/images/site-logo.png"
                width={118}
                height={56}
                alt="Kool logistics logo"
              />
              <button
                onClick={toggleMenu}
                className="bg-primary-500 p-[12px] rounded-[12px]"
                aria-label="Close menu"
              >
                <Image
                  src={"/icons/close.svg"}
                  alt="menu"
                  width={15.000835418701172}
                  height={15.000835418701172}
                />
              </button>
            </div>

            <div className="flex flex-col gap-5">
              <ul className="flex flex-col space-y-5">
                {navLinks.map((link) => (
                  <li key={link.route} className="text-lg">
                    <Link
                      href={link.route}
                      className={cn(
                        "px-2",
                        pathname === link.route
                          ? "text-primary-500 font-semibold"
                          : "text-medium text-woodsmoke-800"
                      )}
                      onClick={toggleMenu}
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>

              <div className="flex flex-col gap-4 mt-6">
                <Link href={ROUTES.SIGN_UP} className="w-full">
                  <button className="w-full btn-primary">Sign Up</button>
                </Link>
                <Link href={ROUTES.SIGN_IN} className="w-full text-center">
                  <span className="w-full block text-center py-2">Login</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;