"use client";
import React, { useState } from "react";

interface TrackingCardProps {
  onUserInteraction?: (isInteracting: boolean) => void;
}

const TrackingCard = ({ onUserInteraction }: TrackingCardProps) => {
  const [trackingId, setTrackingId] = useState("");

  const handleTrack = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle tracking logic here
    if (trackingId.trim()) {
      console.log(`Tracking package with ID: ${trackingId}`);
      // You could add API call here or redirect to tracking page
    }
  };

  const handleFocus = () => {
    if (onUserInteraction) onUserInteraction(true);
  };

  const handleBlur = () => {
    if (onUserInteraction) onUserInteraction(false);
  };

  return (
    <div className="w-full max-w-md bg-white/20 backdrop-blur-sm rounded-[14px] p-[16px] sm:p-[18px] md:p-[20px]">
      <h2 className="text-regular font-semibold text-white pb-[12px] sm:pb-[14px] md:pb-[16px]">
        Track Packages
      </h2>

      <form
        onSubmit={handleTrack}
        className="flex flex-col sm:flex-row gap-3 md:gap-4"
      >
        <input
          type="text"
          placeholder="Enter Track ID"
          value={trackingId}
          onChange={(e) => {
            setTrackingId(e.target.value);
            if (onUserInteraction) onUserInteraction(true);
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className="flex-grow px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-white/90 text-woodsmoke-800 placeholder-woodsmoke-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
          required
        />
        <button type="submit" className="btn-primary whitespace-nowrap">
          Track Now
        </button>
      </form>
    </div>
  );
};

export default TrackingCard;
