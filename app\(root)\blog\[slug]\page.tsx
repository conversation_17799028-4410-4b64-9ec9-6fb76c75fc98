'use client'
import React from 'react'
import { notFound } from 'next/navigation'
import BlogPost from '@/components/blog/BlogPost'
import { getBlogPostBySlug } from '../blogPosts'

interface BlogDetailPageProps {
  params: {
    slug: string
  }
}

const BlogDetailPage = ({ params }: BlogDetailPageProps) => {
  const blogData = getBlogPostBySlug(params.slug)
  
  if (!blogData) {
    notFound()
  }

  return (
    <BlogPost
      title={blogData.title}
      excerpt={blogData.excerpt}
      author={blogData.author}
      heroImage={blogData.heroImage}
      introduction={blogData.introduction}
      sections={blogData.sections}
    />
  )
}

export default BlogDetailPage
