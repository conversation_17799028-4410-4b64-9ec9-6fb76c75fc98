'use client'
import Image from "next/image";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";

const LogoCard: React.FC = () => {
  return (
    <div className="w-[236px] h-[120px] flex flex-center border-1 rounded-[10px] border-woodsmoke-200 transition-all duration-300 hover:border-woodsmoke-300 hover:shadow-md">
      <Image
        src={"/icons/partner-logo.svg"}
        width={176}
        height={29.600000381469727}
        alt="partner-logo"
        className="transition-opacity duration-300 hover:opacity-80"
      />
    </div>
  );
};

const PartnerLogoSection = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    if (!containerRef.current || !scrollRef.current) return;

    const scrollElement = scrollRef.current;

    // Get the width of the scrolling content
    const scrollWidth = scrollElement.scrollWidth;

    // Create the infinite scroll animation
    const tl = gsap.timeline({ repeat: -1 });
    timelineRef.current = tl;

    tl.set(scrollElement, { x: 0 }).to(scrollElement, {
      x: -scrollWidth / 2,
      duration: 20,
      ease: "none",
    });

    // Cleanup function
    return () => {
      tl.kill();
    };
  }, []);

  const handleMouseEnter = () => {
    if (timelineRef.current) {
      timelineRef.current.pause();
    }
  };

  const handleMouseLeave = () => {
    if (timelineRef.current) {
      timelineRef.current.resume();
    }
  };

  return (
    <div
      className="overflow-hidden my-[40px]"
      ref={containerRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        ref={scrollRef}
        className="flex gap-6 whitespace-nowrap"
        style={{ width: "max-content" }}
      >
        {/* First set of logos */}
        {Array.from({ length: 10 }).map((_, idx) => (
          <div key={`first-${idx}`} className="flex-shrink-0">
            <LogoCard />
          </div>
        ))}
        {/* Duplicate set for seamless loop */}
        {Array.from({ length: 10 }).map((_, idx) => (
          <div key={`second-${idx}`} className="flex-shrink-0">
            <LogoCard />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PartnerLogoSection;
