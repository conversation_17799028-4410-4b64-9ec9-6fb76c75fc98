'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const Footer = () => {
  return (
    <footer className="bg-woodsmoke-950 text-white w-full min-h-[340px] flex flex-col">
      {/* Main Footer Content */}
      <div className="flex-1 w-full flex items-center py-6 lg:py-8">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile Layout */}
          <div className="lg:hidden flex flex-col gap-8 py-8">
            {/* Company Logo & Social */}
            <div className="flex flex-col gap-8">
              <div>
                <Image
                  src="/images/footer_logo.png"
                  alt="Kool Logistics Logo"
                  width={160}
                  height={50}
                  className="h-12 w-auto"
                />
              </div>
              <div className="flex gap-3">
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/x.svg"
                    alt="X (Twitter)"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/linkedin.svg"
                    alt="LinkedIn"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/instagram.svg"
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/facebook.svg"
                    alt="Facebook"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/youtube.svg"
                    alt="YouTube"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
              </div>
            </div>

            {/* Say Hello - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Say Hello</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <p className="text-base"><EMAIL></p>
                <p className="text-base">****** 100 975 20 34</p>
              </div>
            </div>

            {/* Useful Link - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Useful Link</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <a href="#" className="text-base hover:text-white transition-colors">About us</a>
                <a href="#" className="text-base hover:text-white transition-colors">Pricing</a>
                <Link href="/QouteForm" className="text-base hover:text-white transition-colors">Quote</Link>
                <a href="#" className="text-base hover:text-white transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Our Services</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <a href="#" className="text-base hover:text-white transition-colors">Logistics</a>
                <a href="#" className="text-base hover:text-white transition-colors">Manufacturing</a>
                <a href="#" className="text-base hover:text-white transition-colors">Production</a>
                <a href="#" className="text-base hover:text-white transition-colors">Automotive</a>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-start justify-between gap-8">
            {/* Company Logo & Social - Desktop */}
            <div className="flex flex-col gap-8 flex-shrink-0">
              <div>
                <Image
                  src="/images/footer_logo.png"
                  alt="Kool Logistics Logo"
                  width={160}
                  height={50}
                  className="h-12 w-auto"
                />
              </div>
              <div className="flex gap-3">
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/x.svg"
                    alt="X (Twitter)"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/linkedin.svg"
                    alt="LinkedIn"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/instagram.svg"
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/facebook.svg"
                    alt="Facebook"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/youtube.svg"
                    alt="YouTube"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
              </div>
            </div>

            {/* Say Hello - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Say Hello</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <p className="text-base"><EMAIL></p>
                <p className="text-base">****** 100 975 20 34</p>
              </div>
            </div>

            {/* Useful Link - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Useful Link</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <a href="#" className="text-base hover:text-white transition-colors">About us</a>
                <a href="#" className="text-base hover:text-white transition-colors">Pricing</a>
                <Link href="/QouteForm" className="text-base hover:text-white transition-colors">Quote</Link>
                <a href="#" className="text-base hover:text-white transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-lg">Our Services</h3>
              <div className="flex flex-col gap-4 text-[#B0B0B0]">
                <a href="#" className="text-base hover:text-white transition-colors">Logistics</a>
                <a href="#" className="text-base hover:text-white transition-colors">Manufacturing</a>
                <a href="#" className="text-base hover:text-white transition-colors">Production</a>
                <a href="#" className="text-base hover:text-white transition-colors">Automotive</a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-[#F57D1C] w-full py-4 lg:py-0 lg:h-14">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 lg:gap-0 items-center text-center lg:text-left h-full">
            <p className="text-white font-manrope font-normal text-sm">
              Copyright © 2025 KOOL LOGISTICS. All Rights Reserved.
            </p>
            <div className="flex gap-8 text-white">
              <a href="#" className="font-manrope font-normal text-sm hover:text-gray-200 transition-colors">Terms of Service</a>
              <a href="#" className="font-manrope font-normal text-sm hover:text-gray-200 transition-colors">Privacy Policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
