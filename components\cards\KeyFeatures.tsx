import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

interface featuresObjType {
  icon: string;
  title: string;
  width: number;
  height: number;
}

const featuresObj = [
  {
    icon: "/icons/Headset.svg",
    title: "24/7 Support",
    width: 19.500072479248047,
    height: 21,
  },
  {
    icon: "/icons/money-primary.svg",
    title: "Reliable Service Fee",
    width: 22.5,
    height: 13.5,
  },
  {
    icon: "/icons/Clock.svg",
    title: "On-Time Delivery",
    width: 19.500003814697266,
    height: 19.500003814697266,
  },
  {
    icon: "/icons/Truck.svg",
    title: "Quality Moving",
    width: 22.500003814697266,
    height: 15.755882263183594,
  },
];

const Features: React.FC<featuresObjType> = (props) => {
  return (
    <div className="flex items-center">
      <div className="bg-primary-50 rounded-[8px] flex items-center justify-center w-[36px] h-[36px] sm:w-[40px] sm:h-[40px] mr-[12px] sm:mr-[16px]">
        <Image
          src={props.icon}
          alt={props.title}
          width={props.width}
          height={props.height}
        />
      </div>
      <span className="text-regular font-medium">{props.title}</span>
    </div>
  );
};

const KeyFeatures = () => {
  return (
    <div>
      <div className="flex flex-wrap">
        {featuresObj.map((feature, index) => (
          <div
            key={index}
            className={cn(
              "w-full sm:w-1/2 md:w-1/4 px-4 mb-4 sm:mb-6 md:mb-0",
              featuresObj.length === index + 1
                ? "border-0"
                : "md:border-r-1 md:border-r-woodsmoke-200"
            )}
          >
            <Features {...feature} />
          </div>
        ))}
      </div>
      <div className="w-full border-b-1 border-woodsmoke-200 mt-[20px]" />
    </div>
  );
};

export default KeyFeatures;
