@import "tailwindcss";
@import "tw-animate-css";

@import 'primereact/resources/themes/lara-light-blue/theme.css';
@import 'primereact/resources/primereact.min.css';
@import 'primeicons/primeicons.css';


@custom-variant dark (&:is(.dark *));

@plugin "@tailwindcss/typography";

@theme {
  --color-primary-50: #fff3ed;
  --color-primary-100: #feeed6;
  --color-primary-200: #fbbf76;
  --color-primary-400: #f8993f;
  --color-primary-500: #f57d1c;

  --color-woodsmoke-50: #f6f6f6;
  --color-woodsmoke-100: #e7e7e7;
  --color-woodsmoke-200: #d1d1d1;
  --color-woodsmoke-300: #b0b0b0;
  --color-woodsmoke-400: #888888;
  --color-woodsmoke-500: #6d6d6d;
  --color-woodsmoke-600: #5d5d5d;
  --color-woodsmoke-700: #4f4f4f;
  --color-woodsmoke-800: #454545;
  --color-woodsmoke-950: #171717;

  --font-manrope: var(--font-manrope);
}

@utility h1-semibold {
  @apply text-[32px] sm:text-[40px] md:text-[48px] lg:text-[56px] font-semibold leading-[120%] tracking-[-1%];
}

@utility h2-semibold {
  @apply text-[28px] sm:text-[32px] md:text-[40px] lg:text-[48px] font-semibold leading-[120%] tracking-[0%];
}

@utility h3-semibold {
  @apply text-[24px] sm:text-[28px] md:text-[32px] lg:text-[40px] font-semibold leading-[120%] tracking-[0%];
}

@utility h4-semibold {
  @apply text-[20px] sm:text-[24px] md:text-[28px] lg:text-[32px] font-semibold leading-[120%] tracking-[0%];
}

@utility h5-semibold {
  @apply text-[18px] sm:text-[20px] md:text-[22px] lg:text-[24px] font-semibold leading-[120%] tracking-[0%];
}

@utility text-large {
  @apply text-[16px] sm:text-[18px] md:text-[20px] leading-[150%] tracking-[0%];
}

@utility text-medium {
  @apply text-[15px] sm:text-[16px] md:text-[18px] leading-[150%] tracking-[0%];
}

@utility text-regular {
  @apply text-[14px] sm:text-[15px] md:text-[16px] leading-[150%] tracking-[0%];
}

@utility text-small {
  @apply text-[12px] sm:text-[13px] md:text-[14px] leading-[150%] tracking-[0%];
}

@utility text-button {
  @apply text-[14px] sm:text-[15px] md:text-[16px] font-semibold leading-[150%] tracking-[0%];
}

@utility btn-primary {
  @apply bg-primary-500 text-white text-button px-[16px] py-[10px] sm:px-[18px] sm:py-[11px] md:px-[20px] md:py-[12px] rounded-[12px] cursor-pointer transition-all;
}

@utility btn-primary-no-bg {
  @apply text-primary-500 text-button px-[16px] py-[10px] sm:px-[18px] sm:py-[11px] md:px-[20px] md:py-[12px] cursor-pointer transition-all;
}

@utility btn-secondary {
  @apply bg-woodsmoke-100 text-woodsmoke-950 text-button px-[16px] py-[10px] sm:px-[18px] sm:py-[11px] md:px-[20px] md:py-[12px] rounded-[12px] cursor-pointer transition-all;
}

@utility btn-secondary-no-bg {
  @apply text-woodsmoke-950 text-button px-[16px] py-[10px] sm:px-[18px] sm:py-[11px] md:px-[20px] md:py-[12px] cursor-pointer transition-all;
}

@utility flex-center {
  @apply flex justify-center items-center;
}

@utility flex-between {
  @apply flex justify-between items-center;
}

@utility flex-start {
  @apply flex justify-start items-center;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hide scrollbar for all browsers */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit browsers (Chrome, Safari, Edge) */
}

html, body {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  overflow-x: hidden; /* Prevent horizontal scroll */
}

html::-webkit-scrollbar, body::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}
