import HeroSection from "@/components/cards/HeroSection";
import KeyFeatures from "@/components/cards/KeyFeatures";
import LandingPageContact from "@/components/cards/LandingPageContact";
import PartnerLogoSection from "@/components/cards/PartnerLogoSection";
import ServicesProcessSection from "@/components/cards/ServicesProcessSection";
import ServicesSection from "@/components/cards/ServicesSection";
import TestimonialSection from "@/components/cards/TestimonialSection";
import React from "react";

const RootPage = () => {
  return (
    <>
      {/* HeroSection is full-width and not constrained by container */}
      <HeroSection />

      {/* Any additional content should be wrapped in a container */}
      <div className="container mx-auto px-4 py-[40px]">
        {/* Other page content goes here */}
        <KeyFeatures />
        <ServicesSection />
        <ServicesProcessSection />
        <TestimonialSection />
      </div>
      <PartnerLogoSection />
      <div className="container mx-auto px-4 py-[40px]">
        <LandingPageContact />
      </div>
    </>
  );
};

export default RootPage;
