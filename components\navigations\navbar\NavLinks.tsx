"use client";

import { navLinks } from "@/constants";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

const NavLinks = ({ isMobileNav = false }: { isMobileNav?: boolean }) => {
  const pathname = usePathname();

  return (
    <ul className={cn("flex gap-[35px]", isMobileNav && "flex-col")}>
      {navLinks.map((link) => (
        <li key={link.route}>
          <Link
            href={link.route}
            className={cn(
              "transition-colors hover:text-primary-500 pb-1",
              pathname === link.route
                ? "text-primary-500 border-b-[3px] font-semibold border-primary-500"
                : "text-medium text-woodsmoke-800"
            )}
          >
            {link.label}
          </Link>
        </li>
      ))}
    </ul>
  );
};

export default NavLinks;