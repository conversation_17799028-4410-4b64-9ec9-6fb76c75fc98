'use client'

import Image from 'next/image'
import Link from 'next/link'
import FAQ from '@/components/FAQ'

const ServiceDetail = () => {
  return (
    <div className="min-h-screen bg-white pt-14">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      {/* Back Button */}
      <div className="pt-6 md:pt-8 pb-6 md:pb-8">
        <div className="max-w-7xl mx-auto px-6">
          <Link
            href="/services"
            className="flex items-center gap-2 text-[#FF6B35] hover:text-[#e55a2b] transition-colors"
          >
            <Image
              src="/icons/Backarrow.svg"
              alt="Back Arrow"
              width={20}
              height={20}
              className="w-4 h-4 md:w-5 md:h-5"
            />
            <span className="text-[12px] md:text-[14px] text-[#171717] font-medium">Back to Services</span>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="pb-12 md:pb-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-8 lg:gap-12 items-start">
            {/* Image - Shows first on mobile, left on desktop */}
            <div className="relative order-1 lg:order-1">
              <Image
                src="/images/container.svg"
                alt="Container shipping"
                width={600}
                height={400}
                className="w-full h-[250px] md:h-[300px] lg:h-[400px] object-cover rounded-[12px] md:rounded-[16px]"
              />
            </div>

            {/* Content - Shows second on mobile, right on desktop */}
            <div className="order-2 lg:order-2 lg:h-[400px] lg:overflow-y-auto lg:scrollbar-hide space-y-6 md:space-y-8" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
              <div>
                <h1 className="text-[24px] md:text-[28px] lg:text-[32px] font-bold text-[#2D2D2D] mb-4 md:mb-6 leading-[1.2]">
                  Fast Moving Consumer Goods
                </h1>

                <div className="space-y-3 md:space-y-4 text-[#666666]">
                  <p className="font-manrope font-normal text-[14px] md:text-[16px] leading-[150%]">
                    For athletes, high altitude produces two contradictory effects on performance.
                    For explosive events Physiological respiration involves the mechanisms that
                    ensure that the composition of the functional.
                  </p>
                  <p className="font-manrope font-normal text-[14px] md:text-[16px] leading-[150%]">
                    The long barrow was built on land previously inhabited in the Mesolithic period.
                    It consisted of a Physical space is often conceived in three linear dimensions,
                    although modern physicists
                  </p>
                </div>

                <ul className="mt-4 md:mt-6 space-y-2 md:space-y-3">
                  <li className="flex items-start gap-2 md:gap-3 text-[#666666] text-[14px] md:text-[16px]">
                    <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <span>For athletes, high altitude produces two contradictory effects on performance</span>
                  </li>
                  <li className="flex items-start gap-2 md:gap-3 text-[#666666] text-[14px] md:text-[16px]">
                    <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <span>The long barrow was built on land previously inhabited in the Mesolithic period</span>
                  </li>
                </ul>
              </div>

              {/* Title Here Section */}
              <div className="pt-1 md:pt-2">
                <h2 className="font-manrope font-semibold text-[20px] leading-[140%] tracking-[0%] text-[#2D2D2D] mb-3 md:mb-4">
                  Title Here
                </h2>

                <div className="text-[#666666]">
                  <p className="font-manrope font-normal text-[16px] leading-[150%] tracking-[0%]">
                    For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
                  </p>
                  <h2 className="font-manrope font-semibold text-[20px] leading-[140%] tracking-[0%] text-[#2D2D2D] mt-6 md:mt-8 mb-3 md:mb-4">
                    Title Here
                  </h2>
                  <p className="font-manrope font-normal text-[16px] leading-[150%] tracking-[0%]">
                    For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default ServiceDetail
